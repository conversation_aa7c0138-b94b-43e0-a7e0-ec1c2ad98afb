#!/usr/bin/env python3
"""
AcroForm 数据提取脚本
从PDF文件中提取AcroForm信息，查找设置了comb numberOfCells属性的字段
"""

import sys
import xml.etree.ElementTree as ET
import re
from typing import List, Dict, Any
import json
import struct

try:
    import PyPDF2
    HAS_PYPDF2 = True
except ImportError:
    HAS_PYPDF2 = False

try:
    import fitz  # PyMuPDF
    HAS_PYMUPDF = True
except ImportError:
    HAS_PYMUPDF = False

try:
    import pdfplumber
    HAS_PDFPLUMBER = True
except ImportError:
    HAS_PDFPLUMBER = False

class AcroFormExtractor:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.acroform_data = None
        self.fields_with_numberOfCells = []

    def extract_acroform_with_pypdf2(self) -> Dict[str, Any]:
        """使用PyPDF2提取AcroForm数据"""
        if not HAS_PYPDF2:
            return None

        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)

                acroform_info = {
                    'has_form': False,
                    'fields': [],
                    'form_object': None
                }

                # 检查是否有表单字段
                if reader.form:
                    acroform_info['has_form'] = True

                    # 获取所有表单字段
                    if hasattr(reader.form, 'fields'):
                        for field_name, field_obj in reader.form.fields.items():
                            field_info = self.analyze_field_object_pypdf2(field_name, field_obj)
                            if field_info:
                                acroform_info['fields'].append(field_info)

                # 尝试从文档根对象获取AcroForm
                if '/Root' in reader.trailer:
                    root = reader.trailer['/Root']
                    if '/AcroForm' in root:
                        acro_form = root['/AcroForm']
                        acroform_info['form_object'] = self.parse_acroform_object_pypdf2(acro_form)

                        # 查找字段数组
                        if '/Fields' in acro_form:
                            fields_array = acro_form['/Fields']
                            for field_ref in fields_array:
                                if hasattr(field_ref, 'get_object'):
                                    field_obj = field_ref.get_object()
                                    field_info = self.analyze_field_object_pypdf2("", field_obj)
                                    if field_info and field_info not in acroform_info['fields']:
                                        acroform_info['fields'].append(field_info)

                return acroform_info

        except Exception as e:
            print(f"PyPDF2提取AcroForm数据时出错: {e}")

        return None

    def analyze_field_object_pypdf2(self, field_name: str, field_obj) -> Dict[str, Any]:
        """分析PyPDF2字段对象"""
        try:
            field_info = {
                'name': field_name,
                'type': 'Unknown',
                'attributes': {},
                'has_numberOfCells': False,
                'numberOfCells_value': None,
                'has_comb': False,
                'comb_value': None
            }

            if hasattr(field_obj, 'items'):
                for key, value in field_obj.items():
                    field_info['attributes'][key] = str(value)

                    # 检查字段类型
                    if key == '/FT':
                        field_info['type'] = str(value)

                    # 检查numberOfCells属性
                    if 'numberOfCells' in str(key).lower() or 'numbercells' in str(key).lower():
                        field_info['has_numberOfCells'] = True
                        field_info['numberOfCells_value'] = str(value)

                    # 检查comb属性
                    if 'comb' in str(key).lower():
                        field_info['has_comb'] = True
                        field_info['comb_value'] = str(value)

                    # 检查Ff (Field flags) 中的comb标志
                    if key == '/Ff':
                        try:
                            flags = int(str(value))
                            # Comb标志通常是第24位 (0x1000000)
                            if flags & 0x1000000:
                                field_info['has_comb'] = True
                                field_info['comb_value'] = 'True (from Ff flags)'
                        except:
                            pass

            return field_info

        except Exception as e:
            print(f"分析字段对象时出错: {e}")
            return None

    def parse_acroform_object_pypdf2(self, acro_form) -> Dict[str, Any]:
        """解析AcroForm对象"""
        try:
            form_info = {
                'attributes': {},
                'has_fields': False,
                'fields_count': 0
            }

            if hasattr(acro_form, 'items'):
                for key, value in acro_form.items():
                    form_info['attributes'][key] = str(value)

                    if key == '/Fields':
                        form_info['has_fields'] = True
                        if hasattr(value, '__len__'):
                            form_info['fields_count'] = len(value)

            return form_info

        except Exception as e:
            print(f"解析AcroForm对象时出错: {e}")
            return None

    def extract_acroform_with_pymupdf(self) -> Dict[str, Any]:
        """使用PyMuPDF提取AcroForm数据"""
        if not HAS_PYMUPDF:
            return None

        try:
            doc = fitz.open(self.pdf_path)

            acroform_info = {
                'has_form': False,
                'fields': [],
                'widgets': []
            }

            # 检查是否有表单
            if doc.is_form_pdf:
                acroform_info['has_form'] = True

            # 遍历所有页面获取表单字段
            for page_num in range(len(doc)):
                page = doc[page_num]
                widgets = page.widgets()

                for widget in widgets:
                    widget_info = self.analyze_widget_pymupdf(widget, page_num)
                    if widget_info:
                        acroform_info['widgets'].append(widget_info)

            # 获取表单字段信息
            try:
                fields = doc.get_form_fields()
                for field_name, field_info in fields.items():
                    field_data = self.analyze_field_pymupdf(field_name, field_info)
                    if field_data:
                        acroform_info['fields'].append(field_data)
            except:
                pass

            doc.close()
            return acroform_info

        except Exception as e:
            print(f"PyMuPDF提取AcroForm数据时出错: {e}")

        return None

    def analyze_widget_pymupdf(self, widget, page_num: int) -> Dict[str, Any]:
        """分析PyMuPDF widget对象"""
        try:
            widget_info = {
                'page': page_num,
                'field_name': getattr(widget, 'field_name', 'Unknown'),
                'field_type': getattr(widget, 'field_type', 'Unknown'),
                'field_value': getattr(widget, 'field_value', ''),
                'rect': getattr(widget, 'rect', None),
                'has_numberOfCells': False,
                'numberOfCells_value': None,
                'has_comb': False,
                'comb_value': None,
                'all_attributes': {}
            }

            # 获取所有可用属性
            for attr in dir(widget):
                if not attr.startswith('_'):
                    try:
                        value = getattr(widget, attr)
                        if not callable(value):
                            widget_info['all_attributes'][attr] = str(value)

                            # 检查numberOfCells相关属性
                            if 'numbercells' in attr.lower() or 'numberOfCells' in attr:
                                widget_info['has_numberOfCells'] = True
                                widget_info['numberOfCells_value'] = str(value)

                            # 检查comb相关属性
                            if 'comb' in attr.lower():
                                widget_info['has_comb'] = True
                                widget_info['comb_value'] = str(value)
                    except:
                        continue

            return widget_info

        except Exception as e:
            print(f"分析widget时出错: {e}")
            return None

    def analyze_field_pymupdf(self, field_name: str, field_info) -> Dict[str, Any]:
        """分析PyMuPDF字段信息"""
        try:
            field_data = {
                'name': field_name,
                'info': field_info,
                'has_numberOfCells': False,
                'numberOfCells_value': None,
                'has_comb': False,
                'comb_value': None
            }

            # 检查字段信息中的numberOfCells和comb属性
            if isinstance(field_info, dict):
                for key, value in field_info.items():
                    if 'numbercells' in str(key).lower() or 'numberOfCells' in str(key):
                        field_data['has_numberOfCells'] = True
                        field_data['numberOfCells_value'] = str(value)

                    if 'comb' in str(key).lower():
                        field_data['has_comb'] = True
                        field_data['comb_value'] = str(value)

            return field_data

        except Exception as e:
            print(f"分析字段信息时出错: {e}")
            return None

    def extract_acroform_raw_search(self) -> Dict[str, Any]:
        """通过原始二进制搜索提取AcroForm数据"""
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()

                acroform_info = {
                    'has_acroform': False,
                    'numberOfCells_matches': [],
                    'comb_matches': [],
                    'field_matches': []
                }

                # 搜索AcroForm相关的模式
                acroform_patterns = [
                    rb'/AcroForm',
                    rb'/Fields',
                    rb'/FT\s*/Tx',  # 文本字段
                    rb'/Ff\s*\d+',  # 字段标志
                ]

                for pattern in acroform_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        acroform_info['has_acroform'] = True
                        break

                # 搜索numberOfCells相关模式
                numberOfCells_patterns = [
                    rb'numberOfCells[^>]*["\']([^"\']*)["\']',
                    rb'/numberOfCells\s*(\d+)',
                    rb'numberOfCells\s*[:=]\s*(\d+)',
                ]

                for pattern in numberOfCells_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        try:
                            decoded = match.decode('utf-8', errors='ignore')
                            if decoded not in acroform_info['numberOfCells_matches']:
                                acroform_info['numberOfCells_matches'].append(decoded)
                        except:
                            continue

                # 搜索comb相关模式
                comb_patterns = [
                    rb'comb[^>]*["\']([^"\']*)["\']',
                    rb'/Comb\s*true',
                    rb'comb\s*[:=]\s*(true|false|\d+)',
                    rb'/Ff\s*(\d+)',  # 字段标志，可能包含comb标志
                ]

                for pattern in comb_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        try:
                            decoded = match.decode('utf-8', errors='ignore')
                            # 如果是Ff标志，检查是否包含comb位
                            if pattern == rb'/Ff\s*(\d+)':
                                try:
                                    flags = int(decoded)
                                    if flags & 0x1000000:  # Comb标志位
                                        decoded = f"Ff={decoded} (contains comb flag)"
                                except:
                                    continue

                            if decoded not in acroform_info['comb_matches']:
                                acroform_info['comb_matches'].append(decoded)
                        except:
                            continue

                # 搜索字段定义
                field_patterns = [
                    rb'<<[^>]*?/T\s*\(([^)]*)\)[^>]*?>>',  # 字段名称
                    rb'/FT\s*/Tx[^>]*?/T\s*\(([^)]*)\)',  # 文本字段名称
                ]

                for pattern in field_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    for match in matches:
                        try:
                            decoded = match.decode('utf-8', errors='ignore')
                            if decoded not in acroform_info['field_matches']:
                                acroform_info['field_matches'].append(decoded)
                        except:
                            continue

                return acroform_info

        except Exception as e:
            print(f"原始搜索AcroForm数据时出错: {e}")

        return None

    def extract_acroform_data(self) -> Dict[str, Any]:
        """提取AcroForm数据，尝试多种方法"""
        print("正在提取AcroForm数据...")

        all_data = {
            'pypdf2_data': None,
            'pymupdf_data': None,
            'raw_search_data': None,
            'combined_fields': []
        }

        # 方法1: 使用PyPDF2
        if HAS_PYPDF2:
            print("尝试使用PyPDF2...")
            pypdf2_data = self.extract_acroform_with_pypdf2()
            if pypdf2_data:
                print("✓ PyPDF2成功提取AcroForm数据")
                all_data['pypdf2_data'] = pypdf2_data

        # 方法2: 使用PyMuPDF
        if HAS_PYMUPDF:
            print("尝试使用PyMuPDF...")
            pymupdf_data = self.extract_acroform_with_pymupdf()
            if pymupdf_data:
                print("✓ PyMuPDF成功提取AcroForm数据")
                all_data['pymupdf_data'] = pymupdf_data

        # 方法3: 原始二进制搜索
        print("尝试原始二进制搜索...")
        raw_data = self.extract_acroform_raw_search()
        if raw_data:
            print("✓ 原始搜索找到相关数据")
            all_data['raw_search_data'] = raw_data

        # 检查是否找到任何数据
        if not any([all_data['pypdf2_data'], all_data['pymupdf_data'], all_data['raw_search_data']]):
            print("✗ 未能提取到AcroForm数据")
            return None

        return all_data

    def analyze_acroform_data(self, acroform_data: Dict[str, Any]):
        """分析AcroForm数据，查找numberOfCells和comb字段"""
        if not acroform_data:
            return

        # 分析PyPDF2数据
        if acroform_data.get('pypdf2_data'):
            print("\n=== 分析PyPDF2数据 ===")
            pypdf2_data = acroform_data['pypdf2_data']

            if pypdf2_data.get('fields'):
                for field in pypdf2_data['fields']:
                    if field.get('has_numberOfCells') or field.get('has_comb'):
                        field['source'] = 'PyPDF2'
                        self.fields_with_numberOfCells.append(field)
                        print(f"找到字段: {field.get('name', 'Unknown')} (numberOfCells: {field.get('has_numberOfCells')}, comb: {field.get('has_comb')})")

        # 分析PyMuPDF数据
        if acroform_data.get('pymupdf_data'):
            print("\n=== 分析PyMuPDF数据 ===")
            pymupdf_data = acroform_data['pymupdf_data']

            if pymupdf_data.get('widgets'):
                for widget in pymupdf_data['widgets']:
                    if widget.get('has_numberOfCells') or widget.get('has_comb'):
                        widget['source'] = 'PyMuPDF-widget'
                        self.fields_with_numberOfCells.append(widget)
                        print(f"找到widget: {widget.get('field_name', 'Unknown')} (numberOfCells: {widget.get('has_numberOfCells')}, comb: {widget.get('has_comb')})")

            if pymupdf_data.get('fields'):
                for field in pymupdf_data['fields']:
                    if field.get('has_numberOfCells') or field.get('has_comb'):
                        field['source'] = 'PyMuPDF-field'
                        self.fields_with_numberOfCells.append(field)
                        print(f"找到字段: {field.get('name', 'Unknown')} (numberOfCells: {field.get('has_numberOfCells')}, comb: {field.get('has_comb')})")

        # 分析原始搜索数据
        if acroform_data.get('raw_search_data'):
            print("\n=== 分析原始搜索数据 ===")
            raw_data = acroform_data['raw_search_data']

            if raw_data.get('numberOfCells_matches'):
                for match in raw_data['numberOfCells_matches']:
                    field_info = {
                        'source': 'Raw-search',
                        'type': 'numberOfCells_match',
                        'value': match,
                        'has_numberOfCells': True,
                        'numberOfCells_value': match
                    }
                    self.fields_with_numberOfCells.append(field_info)
                    print(f"找到numberOfCells匹配: {match}")

            if raw_data.get('comb_matches'):
                for match in raw_data['comb_matches']:
                    field_info = {
                        'source': 'Raw-search',
                        'type': 'comb_match',
                        'value': match,
                        'has_comb': True,
                        'comb_value': match
                    }
                    self.fields_with_numberOfCells.append(field_info)
                    print(f"找到comb匹配: {match}")

    def run_analysis(self):
        """运行完整的AcroForm分析"""
        # 提取AcroForm数据
        self.acroform_data = self.extract_acroform_data()

        if not self.acroform_data:
            print("无法提取AcroForm数据，可能此PDF不包含表单或表单数据已损坏")
            return

        print(f"成功提取AcroForm数据")

        # 保存原始数据到文件以供检查
        try:
            with open('acroform_data_raw.json', 'w', encoding='utf-8') as f:
                # 创建可序列化的数据副本
                serializable_data = self.make_serializable(self.acroform_data)
                json.dump(serializable_data, f, indent=2, ensure_ascii=False)
            print("原始AcroForm数据已保存到 acroform_data_raw.json")
        except Exception as e:
            print(f"保存原始AcroForm数据时出错: {e}")

        # 分析数据
        self.analyze_acroform_data(self.acroform_data)

    def make_serializable(self, obj):
        """将对象转换为可JSON序列化的格式"""
        if isinstance(obj, dict):
            return {k: self.make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            try:
                json.dumps(obj)
                return obj
            except:
                return str(obj)



    def print_results(self):
        """打印分析结果"""
        print("\n" + "="*60)
        print("AcroForm分析结果 - numberOfCells和comb字段")
        print("="*60)

        if not self.fields_with_numberOfCells:
            print("未找到任何设置了numberOfCells或comb属性的字段")

            # 提供一些调试信息
            if self.acroform_data:
                print(f"\n调试信息:")
                print(f"- 是否有PyPDF2数据: {bool(self.acroform_data.get('pypdf2_data'))}")
                print(f"- 是否有PyMuPDF数据: {bool(self.acroform_data.get('pymupdf_data'))}")
                print(f"- 是否有原始搜索数据: {bool(self.acroform_data.get('raw_search_data'))}")

                if self.acroform_data.get('raw_search_data'):
                    raw_data = self.acroform_data['raw_search_data']
                    print(f"- 原始搜索找到AcroForm: {raw_data.get('has_acroform', False)}")
                    print(f"- numberOfCells匹配数: {len(raw_data.get('numberOfCells_matches', []))}")
                    print(f"- comb匹配数: {len(raw_data.get('comb_matches', []))}")
                    print(f"- 字段匹配数: {len(raw_data.get('field_matches', []))}")
            return

        print(f"找到 {len(self.fields_with_numberOfCells)} 个设置了numberOfCells或comb属性的字段:\n")

        for i, field in enumerate(self.fields_with_numberOfCells, 1):
            print(f"字段 {i}:")
            print("-" * 30)

            if 'source' in field:
                print(f"  数据源: {field['source']}")
            if 'name' in field:
                print(f"  名称: {field['name']}")
            if 'field_name' in field:
                print(f"  字段名: {field['field_name']}")
            if 'type' in field:
                print(f"  类型: {field['type']}")
            if 'field_type' in field:
                print(f"  字段类型: {field['field_type']}")
            if 'has_numberOfCells' in field and field['has_numberOfCells']:
                print(f"  ✓ 有numberOfCells: {field.get('numberOfCells_value', 'Unknown')}")
            if 'has_comb' in field and field['has_comb']:
                print(f"  ✓ 有comb: {field.get('comb_value', 'Unknown')}")
            if 'page' in field:
                print(f"  页面: {field['page']}")
            if 'rect' in field:
                print(f"  位置: {field['rect']}")
            if 'value' in field:
                print(f"  值: {field['value']}")
            if 'all_attributes' in field:
                print(f"  所有属性: {json.dumps(field['all_attributes'], indent=4, ensure_ascii=False)}")

            print()

    def save_results_to_file(self, output_file: str = "numberOfCells_fields.json"):
        """将结果保存到JSON文件"""
        try:
            result = {
                'pdf_file': self.pdf_path,
                'total_fields_found': len(self.fields_with_numberOfCells),
                'fields': self.make_serializable(self.fields_with_numberOfCells),
                'acroform_data_available': bool(self.acroform_data),
                'analysis_summary': {
                    'pypdf2_available': bool(self.acroform_data and self.acroform_data.get('pypdf2_data')),
                    'pymupdf_available': bool(self.acroform_data and self.acroform_data.get('pymupdf_data')),
                    'raw_search_available': bool(self.acroform_data and self.acroform_data.get('raw_search_data'))
                }
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            print(f"结果已保存到: {output_file}")

        except Exception as e:
            print(f"保存结果时出错: {e}")

def main():
    if len(sys.argv) < 2:
        print("使用方法: python readXFA.py <PDF文件路径>")
        print("示例: python readXFA.py fw9.pdf")
        print("功能: 分析PDF文件中的AcroForm，查找设置了numberOfCells或comb属性的字段")
        sys.exit(1)

    pdf_path = sys.argv[1]

    print(f"分析PDF文件: {pdf_path}")
    print("="*50)

    # 检查可用的库
    print("检查可用的PDF处理库:")
    print(f"  PyPDF2: {'✓' if HAS_PYPDF2 else '✗'}")
    print(f"  PyMuPDF: {'✓' if HAS_PYMUPDF else '✗'}")
    print(f"  pdfplumber: {'✓' if HAS_PDFPLUMBER else '✗'}")

    if not any([HAS_PYPDF2, HAS_PYMUPDF, HAS_PDFPLUMBER]):
        print("\n警告: 未安装任何PDF处理库，将尝试原始二进制搜索")
        print("建议安装: pip install PyMuPDF 或 pip install PyPDF2")

    print()

    # 创建提取器并分析
    extractor = AcroFormExtractor(pdf_path)
    extractor.run_analysis()
    extractor.print_results()
    extractor.save_results_to_file()

if __name__ == "__main__":
    main()